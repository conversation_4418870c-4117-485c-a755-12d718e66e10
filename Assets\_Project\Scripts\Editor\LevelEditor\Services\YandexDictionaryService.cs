using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Networking;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    /// <summary>
    /// Сервис для валидации слов через Yandex Dictionary API
    /// </summary>
    public class YandexDictionaryService : IWordValidationService
    {
        private const string YANDEX_DICT_API_URL = "https://dictionary.yandex.net/api/v1/dicservice.json/lookup";
        private const string LANG_PAIR = "ru-ru"; // Русский-русский для проверки существования слова
        
        private readonly string _apiKey;
        private readonly Dictionary<string, bool> _validationCache;
        private readonly HashSet<string> _invalidWords;
        private readonly int _requestTimeout;
        
        public string ServiceName => "Yandex Dictionary";
        public int MinWordLength => 2;
        public int MaxWordLength => 20;
        
        public YandexDictionaryService(string apiKey, int timeoutSeconds = 10)
        {
            _apiKey = apiKey;
            _requestTimeout = timeoutSeconds;
            _validationCache = new Dictionary<string, bool>();
            _invalidWords = new HashSet<string>();
        }
        
        public async UniTask<bool> IsServiceAvailableAsync()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                Debug.LogWarning("Yandex Dictionary API key is not set");
                return false;
            }
            
            try
            {
                // Тестируем с простым словом
                return await IsValidWordAsync("тест");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Yandex Dictionary service unavailable: {ex.Message}");
                return false;
            }
        }
        
        public async UniTask<bool> IsValidWordAsync(string word)
        {
            if (string.IsNullOrEmpty(word) || word.Length < MinWordLength || word.Length > MaxWordLength)
                return false;
                
            if (string.IsNullOrEmpty(_apiKey))
                return false;
            
            string upperWord = word.ToUpper();
            
            // Проверяем кэш
            if (_validationCache.ContainsKey(upperWord))
                return _validationCache[upperWord];
                
            if (_invalidWords.Contains(upperWord))
                return false;
            
            try
            {
                string url = $"{YANDEX_DICT_API_URL}?key={_apiKey}&lang={LANG_PAIR}&text={UnityWebRequest.EscapeURL(word)}";
                
                using (UnityWebRequest request = UnityWebRequest.Get(url))
                {
                    request.timeout = _requestTimeout;
                    await request.SendWebRequest().ToUniTask();
                    
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string jsonResponse = request.downloadHandler.text;
                        bool isValid = ParseYandexResponse(jsonResponse, word);
                        
                        // Кэшируем результат
                        if (isValid)
                        {
                            _validationCache[upperWord] = true;
                        }
                        else
                        {
                            _invalidWords.Add(upperWord);
                            _validationCache[upperWord] = false;
                        }
                        
                        return isValid;
                    }
                    else
                    {
                        Debug.LogWarning($"Yandex Dictionary API error: {request.error}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Yandex Dictionary API exception for word '{word}': {ex.Message}");
                return false;
            }
        }
        
        public async UniTask<Dictionary<string, bool>> ValidateWordsAsync(IEnumerable<string> words)
        {
            var results = new Dictionary<string, bool>();
            var wordsToCheck = new List<string>();
            
            // Сначала проверяем кэш
            foreach (string word in words)
            {
                string upperWord = word.ToUpper();
                if (_validationCache.ContainsKey(upperWord))
                {
                    results[upperWord] = _validationCache[upperWord];
                }
                else if (_invalidWords.Contains(upperWord))
                {
                    results[upperWord] = false;
                }
                else
                {
                    wordsToCheck.Add(word);
                }
            }
            
            // Проверяем оставшиеся слова с задержкой между запросами
            foreach (string word in wordsToCheck)
            {
                bool isValid = await IsValidWordAsync(word);
                results[word.ToUpper()] = isValid;
                
                // Задержка между запросами для соблюдения лимитов API
                await UniTask.Delay(200);
            }
            
            return results;
        }
        
        public void ClearCache()
        {
            int cacheSize = _validationCache.Count + _invalidWords.Count;
            _validationCache.Clear();
            _invalidWords.Clear();
            Debug.Log($"🧹 Yandex Dictionary cache cleared ({cacheSize} entries removed)");
        }
        
        public string GetCacheStatistics()
        {
            return $"Valid words: {_validationCache.Count(kv => kv.Value)}, Invalid words: {_invalidWords.Count}, Total cache: {_validationCache.Count}";
        }
        
        /// <summary>
        /// Парсит ответ от Yandex Dictionary API
        /// </summary>
        private bool ParseYandexResponse(string jsonResponse, string originalWord)
        {
            try
            {
                // Простая проверка: если в ответе есть "def", значит слово найдено
                if (jsonResponse.Contains("\"def\":[") && !jsonResponse.Contains("\"def\":[]"))
                {
                    // Дополнительная проверка: убеждаемся, что это не пустой массив определений
                    int defStart = jsonResponse.IndexOf("\"def\":[");
                    if (defStart != -1)
                    {
                        int defEnd = jsonResponse.IndexOf("]", defStart);
                        if (defEnd != -1)
                        {
                            string defSection = jsonResponse.Substring(defStart + 7, defEnd - defStart - 7);
                            return !string.IsNullOrWhiteSpace(defSection) && defSection.Trim() != "";
                        }
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Error parsing Yandex response for '{originalWord}': {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Проверяет, является ли слово русским
        /// </summary>
        private bool IsRussianWord(string word)
        {
            if (string.IsNullOrEmpty(word))
                return false;
                
            foreach (char c in word)
            {
                if (!((c >= 'а' && c <= 'я') || (c >= 'А' && c <= 'Я') || c == 'ё' || c == 'Ё'))
                    return false;
            }
            
            return true;
        }
    }
}
