using UnityEngine;
using UnityEditor;

public class TestBonusWordsSystem
{
    [MenuItem("Tools/Test Bonus Words System")]
    public static void TestSystem()
    {
        Debug.Log("🧪 Testing Bonus Words System...");
        
        // 1. Проверяем загрузку базы данных
        var database = AssetDatabase.LoadAssetAtPath<BonusWordsDatabase>("Assets/_Project/Prefabs/SO/WordDistribution/BonusWordsDatabase.asset");
        if (database == null)
        {
            Debug.LogError("❌ Failed to load BonusWordsDatabase from Assets/_Project/Prefabs/SO/WordDistribution/");
            return;
        }
        Debug.Log($"✅ Database loaded: {database.TotalWordsCount} words");
        
        // 2. Проверяем BonusWordsService
        var service = BonusWordsService.Instance;
        if (service == null)
        {
            Debug.LogError("❌ BonusWordsService.Instance is null");
            return;
        }
        Debug.Log("✅ BonusWordsService initialized");
        
        // 3. Тестируем добавление слова
        string testWord = "ТЕСТ";
        bool added = service.AddBonusWord(testWord, "Test");
        Debug.Log($"✅ Added test word '{testWord}': {added}");
        
        // 4. Тестируем проверку слова
        bool isBonusWord = service.IsBonusWord(testWord);
        Debug.Log($"✅ Is '{testWord}' a bonus word: {isBonusWord}");
        
        // 5. Тестируем обработку найденного слова
        if (isBonusWord)
        {
            service.ProcessFoundBonusWord(testWord);
            Debug.Log($"✅ Processed found word '{testWord}'");
        }
        
        // 6. Показываем статистику
        var stats = service.GetStats();
        Debug.Log($"📊 Stats - Total: {stats.TotalWords}, Found: {stats.FoundWords}, Percentage: {stats.FoundPercentage:F1}%");
        
        Debug.Log("🎉 Bonus Words System test completed!");
    }
}
