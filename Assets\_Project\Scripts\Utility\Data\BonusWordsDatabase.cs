using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[Serializable]
public class BonusWordEntry
{
    public string Word;
    public bool IsFound; // Найдено ли игроком
    public DateTime FoundDate; // Когда найдено
    public string Source; // Откуда слово (Local, External, Yandex, OpenAI)
    
    public BonusWordEntry(string word, string source = "Local")
    {
        Word = word.ToUpper();
        IsFound = false;
        Source = source;
    }
    
    public void MarkAsFound()
    {
        IsFound = true;
        FoundDate = DateTime.Now;
    }
}

[CreateAssetMenu(menuName = "Scriptable Objects/Bonus Words Database", fileName = "BonusWordsDatabase")]
public class BonusWordsDatabase : ScriptableObject
{
    [SerializeField] private List<BonusWordEntry> bonusWords = new List<BonusWordEntry>();
    [SerializeField] private HashSet<string> wordsHashSet = new HashSet<string>();
    
    public List<BonusWordEntry> BonusWords => bonusWords;
    public int TotalWordsCount => bonusWords.Count;
    public int FoundWordsCount => bonusWords.Count(w => w.IsFound);
    
    private void OnEnable()
    {
        RefreshHashSet();
    }
    
    /// <summary>
    /// Обновляет HashSet для быстрого поиска
    /// </summary>
    private void RefreshHashSet()
    {
        wordsHashSet.Clear();
        foreach (var entry in bonusWords)
        {
            wordsHashSet.Add(entry.Word.ToUpper());
        }
    }
    
    /// <summary>
    /// Проверяет, существует ли бонусное слово в базе
    /// </summary>
    public bool ContainsBonusWord(string word)
    {
        if (string.IsNullOrEmpty(word))
            return false;
            
        return wordsHashSet.Contains(word.ToUpper());
    }
    
    /// <summary>
    /// Получает информацию о бонусном слове
    /// </summary>
    public BonusWordEntry GetBonusWordEntry(string word)
    {
        if (string.IsNullOrEmpty(word))
            return null;
            
        string upperWord = word.ToUpper();
        return bonusWords.FirstOrDefault(w => w.Word == upperWord);
    }
    
    /// <summary>
    /// Отмечает бонусное слово как найденное
    /// </summary>
    public bool MarkWordAsFound(string word)
    {
        var entry = GetBonusWordEntry(word);
        if (entry != null && !entry.IsFound)
        {
            entry.MarkAsFound();
            Debug.Log($"🎁 Bonus word marked as found: {word}");
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// Добавляет новое бонусное слово в базу
    /// </summary>
    public bool AddBonusWord(string word, string source = "Local")
    {
        if (string.IsNullOrEmpty(word))
            return false;
            
        string upperWord = word.ToUpper();
        
        if (!wordsHashSet.Contains(upperWord))
        {
            var newEntry = new BonusWordEntry(upperWord, source);
            bonusWords.Add(newEntry);
            wordsHashSet.Add(upperWord);
            
            Debug.Log($"➕ Added new bonus word: {upperWord} (Source: {source})");
            return true;
        }
        
        return false; // Слово уже существует
    }
    
    /// <summary>
    /// Добавляет несколько бонусных слов
    /// </summary>
    public int AddBonusWords(IEnumerable<string> words, string source = "Local")
    {
        int addedCount = 0;
        foreach (string word in words)
        {
            if (AddBonusWord(word, source))
                addedCount++;
        }
        return addedCount;
    }
    
    /// <summary>
    /// Удаляет бонусное слово из базы
    /// </summary>
    public bool RemoveBonusWord(string word)
    {
        if (string.IsNullOrEmpty(word))
            return false;
            
        string upperWord = word.ToUpper();
        var entry = bonusWords.FirstOrDefault(w => w.Word == upperWord);
        
        if (entry != null)
        {
            bonusWords.Remove(entry);
            wordsHashSet.Remove(upperWord);
            Debug.Log($"➖ Removed bonus word: {upperWord}");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Очищает все бонусные слова
    /// </summary>
    public void ClearAllWords()
    {
        bonusWords.Clear();
        wordsHashSet.Clear();
        Debug.Log("🗑️ Cleared all bonus words");
    }
    
    /// <summary>
    /// Сбрасывает статус "найдено" для всех слов
    /// </summary>
    public void ResetFoundStatus()
    {
        foreach (var entry in bonusWords)
        {
            entry.IsFound = false;
        }
        Debug.Log("🔄 Reset found status for all bonus words");
    }
    
    /// <summary>
    /// Получает статистику по бонусным словам
    /// </summary>
    public BonusWordsStats GetStats()
    {
        return new BonusWordsStats
        {
            TotalWords = TotalWordsCount,
            FoundWords = FoundWordsCount,
            NotFoundWords = TotalWordsCount - FoundWordsCount,
            FoundPercentage = TotalWordsCount > 0 ? (float)FoundWordsCount / TotalWordsCount * 100f : 0f
        };
    }
    
    /// <summary>
    /// Получает все найденные слова
    /// </summary>
    public List<BonusWordEntry> GetFoundWords()
    {
        return bonusWords.Where(w => w.IsFound).ToList();
    }
    
    /// <summary>
    /// Получает все ненайденные слова
    /// </summary>
    public List<BonusWordEntry> GetNotFoundWords()
    {
        return bonusWords.Where(w => !w.IsFound).ToList();
    }
    
    /// <summary>
    /// Получает слова по источнику
    /// </summary>
    public List<BonusWordEntry> GetWordsBySource(string source)
    {
        return bonusWords.Where(w => w.Source == source).ToList();
    }
}

[Serializable]
public class BonusWordsStats
{
    public int TotalWords;
    public int FoundWords;
    public int NotFoundWords;
    public float FoundPercentage;
    
    public override string ToString()
    {
        return $"Total: {TotalWords}, Found: {FoundWords} ({FoundPercentage:F1}%), Not Found: {NotFoundWords}";
    }
}
