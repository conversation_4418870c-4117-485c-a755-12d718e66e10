using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Централизованный сервис для работы с бонусными словами
/// Заменяет старую систему хранения бонусных слов в каждом уровне
/// </summary>
public class BonusWordsService : MonoBehaviour
{
    public static BonusWordsService Instance { get; private set; }
    
    [Header("Database Settings")]
    [SerializeField] private BonusWordsDatabase bonusWordsDatabase;
    [SerializeField] private string databasePath = "Assets/_Project/Prefabs/SO/BonusWordsDatabase.asset";
    
    // Events
    public static event Action<string> OnBonusWordFound;
    public static event Action<BonusWordsStats> OnStatsUpdated;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeDatabase();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// Инициализирует базу данных бонусных слов
    /// </summary>
    private void InitializeDatabase()
    {
        if (bonusWordsDatabase == null)
        {
            // Пытаемся загрузить существующую базу через Resources
            bonusWordsDatabase = Resources.Load<BonusWordsDatabase>("BonusWordsDatabase");

            if (bonusWordsDatabase == null)
            {
                Debug.LogWarning($"⚠️ BonusWordsDatabase not found in Resources folder. Please assign it manually or create one.");
            }
            else
            {
                Debug.Log($"✅ BonusWordsDatabase loaded: {bonusWordsDatabase.TotalWordsCount} words");
            }
        }
    }
    
    /// <summary>
    /// Устанавливает базу данных бонусных слов
    /// </summary>
    public void SetDatabase(BonusWordsDatabase database)
    {
        bonusWordsDatabase = database;
        Debug.Log($"📚 BonusWordsDatabase set: {database?.TotalWordsCount ?? 0} words");
    }
    
    /// <summary>
    /// Проверяет, является ли слово бонусным
    /// </summary>
    public bool IsBonusWord(string word)
    {
        if (bonusWordsDatabase == null || string.IsNullOrEmpty(word))
            return false;
            
        return bonusWordsDatabase.ContainsBonusWord(word);
    }
    
    /// <summary>
    /// Отмечает бонусное слово как найденное и возвращает true, если это было новое найденное слово
    /// </summary>
    public bool ProcessFoundBonusWord(string word)
    {
        if (bonusWordsDatabase == null || string.IsNullOrEmpty(word))
            return false;
            
        bool wasNewlyFound = bonusWordsDatabase.MarkWordAsFound(word);
        
        if (wasNewlyFound)
        {
            Debug.Log($"🎁 New bonus word found: {word}");
            OnBonusWordFound?.Invoke(word);
            OnStatsUpdated?.Invoke(bonusWordsDatabase.GetStats());
            
            // Сохраняем изменения в базе данных
            SaveDatabase();
        }
        
        return wasNewlyFound;
    }
    
    /// <summary>
    /// Получает информацию о бонусном слове
    /// </summary>
    public BonusWordEntry GetBonusWordInfo(string word)
    {
        if (bonusWordsDatabase == null)
            return null;
            
        return bonusWordsDatabase.GetBonusWordEntry(word);
    }
    
    /// <summary>
    /// Добавляет новое бонусное слово в базу
    /// </summary>
    public bool AddBonusWord(string word, string source = "Local")
    {
        if (bonusWordsDatabase == null)
            return false;
            
        bool added = bonusWordsDatabase.AddBonusWord(word, source);
        if (added)
        {
            SaveDatabase();
            OnStatsUpdated?.Invoke(bonusWordsDatabase.GetStats());
        }
        
        return added;
    }
    
    /// <summary>
    /// Добавляет несколько бонусных слов
    /// </summary>
    public int AddBonusWords(IEnumerable<string> words, string source = "Local")
    {
        if (bonusWordsDatabase == null)
            return 0;
            
        int addedCount = bonusWordsDatabase.AddBonusWords(words, source);
        if (addedCount > 0)
        {
            SaveDatabase();
            OnStatsUpdated?.Invoke(bonusWordsDatabase.GetStats());
        }
        
        return addedCount;
    }
    
    /// <summary>
    /// Получает статистику по бонусным словам
    /// </summary>
    public BonusWordsStats GetStats()
    {
        if (bonusWordsDatabase == null)
            return new BonusWordsStats();
            
        return bonusWordsDatabase.GetStats();
    }
    
    /// <summary>
    /// Получает все найденные бонусные слова
    /// </summary>
    public List<BonusWordEntry> GetFoundWords()
    {
        if (bonusWordsDatabase == null)
            return new List<BonusWordEntry>();
            
        return bonusWordsDatabase.GetFoundWords();
    }
    
    /// <summary>
    /// Получает все ненайденные бонусные слова
    /// </summary>
    public List<BonusWordEntry> GetNotFoundWords()
    {
        if (bonusWordsDatabase == null)
            return new List<BonusWordEntry>();
            
        return bonusWordsDatabase.GetNotFoundWords();
    }
    
    /// <summary>
    /// Сбрасывает статус "найдено" для всех слов
    /// </summary>
    public void ResetAllFoundStatus()
    {
        if (bonusWordsDatabase == null)
            return;
            
        bonusWordsDatabase.ResetFoundStatus();
        SaveDatabase();
        OnStatsUpdated?.Invoke(bonusWordsDatabase.GetStats());
        Debug.Log("🔄 Reset all bonus words found status");
    }
    
    /// <summary>
    /// Очищает всю базу бонусных слов
    /// </summary>
    public void ClearAllBonusWords()
    {
        if (bonusWordsDatabase == null)
            return;
            
        bonusWordsDatabase.ClearAllWords();
        SaveDatabase();
        OnStatsUpdated?.Invoke(bonusWordsDatabase.GetStats());
        Debug.Log("🗑️ Cleared all bonus words");
    }
    
    /// <summary>
    /// Сохраняет изменения в базе данных
    /// </summary>
    private void SaveDatabase()
    {
        if (bonusWordsDatabase != null)
        {
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(bonusWordsDatabase);
            UnityEditor.AssetDatabase.SaveAssets();
#else
            // В runtime сохранение происходит автоматически через ScriptableObject
            Debug.Log("📚 Database changes saved (runtime mode)");
#endif
        }
    }
    
    /// <summary>
    /// Получает общее количество бонусных слов
    /// </summary>
    public int GetTotalWordsCount()
    {
        return bonusWordsDatabase?.TotalWordsCount ?? 0;
    }
    
    /// <summary>
    /// Получает количество найденных бонусных слов
    /// </summary>
    public int GetFoundWordsCount()
    {
        return bonusWordsDatabase?.FoundWordsCount ?? 0;
    }
    
    /// <summary>
    /// Проверяет, найдено ли конкретное бонусное слово
    /// </summary>
    public bool IsWordFound(string word)
    {
        var entry = GetBonusWordInfo(word);
        return entry?.IsFound ?? false;
    }
}
