using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TMPro;
using VG;

public enum WordType
{
    Correct,    // Правильное слово (основное)
    Bonus,      // Бонусное слово
    Wrong       // Неправильное слово
}

public class WordValidationResult
{
    public WordType Type { get; set; }
    public string Word { get; set; }
    public bool IsValid { get; set; }
    public string Message { get; set; }
}

public class WordValidationService : MonoBehaviour
{
    [Header("Settings")]
    [SerializeField] private float wrongTextInfoDuration = 0.3f;
    [SerializeField] private float bonusTextInfoDuration = 2.0f;
    
    [Header("UI References")]
    [SerializeField] private TextMeshProUGUI wrongTextInfo;
    
    // Events
    public static event Action<string> OnCorrectWordFound;
    public static event Action<string> OnBonusWordFound;
    public static event Action<string> OnWrongWordEntered;
    
    private Coroutine _wrongTextDisplayCoroutine;
    private HashSet<string> _currentLevelWords = new HashSet<string>();
    
    public static WordValidationService Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        if (wrongTextInfo != null)
        {
            wrongTextInfo.SetText(string.Empty);
        }
    }
    
    /// <summary>
    /// Устанавливает слова для текущего уровня
    /// </summary>
    public void SetLevelWords(List<string> mainWords)
    {
        _currentLevelWords.Clear();

        foreach (string word in mainWords)
        {
            _currentLevelWords.Add(word.ToUpper());
        }

        Debug.Log($"🎯 WordValidationService: Set {_currentLevelWords.Count} main words");
    }
    
    /// <summary>
    /// Проверяет введенное слово и возвращает результат
    /// </summary>
    public WordValidationResult ValidateWord(string word)
    {
        if (string.IsNullOrEmpty(word) || word.Length <= 1)
        {
            return new WordValidationResult
            {
                Type = WordType.Wrong,
                Word = word,
                IsValid = false,
                Message = "Word too short"
            };
        }
        
        string upperWord = word.ToUpper();
        
        // Проверяем основные слова
        if (_currentLevelWords.Contains(upperWord))
        {
            return new WordValidationResult
            {
                Type = WordType.Correct,
                Word = upperWord,
                IsValid = true,
                Message = "Correct word found"
            };
        }
        
        // Проверяем бонусные слова через централизованный сервис
        if (BonusWordsService.Instance != null && BonusWordsService.Instance.IsBonusWord(upperWord))
        {
            return new WordValidationResult
            {
                Type = WordType.Bonus,
                Word = upperWord,
                IsValid = true,
                Message = "Bonus word found"
            };
        }
        
        // Неправильное слово
        return new WordValidationResult
        {
            Type = WordType.Wrong,
            Word = upperWord,
            IsValid = false,
            Message = "Word not found"
        };
    }
    
    /// <summary>
    /// Обрабатывает введенное слово и выполняет соответствующие действия
    /// </summary>
    public bool ProcessWord(string word, Letter[] letters = null)
    {
        var result = ValidateWord(word);
        
        switch (result.Type)
        {
            case WordType.Correct:
                return ProcessCorrectWord(result.Word, letters);
                
            case WordType.Bonus:
                ProcessBonusWord(result.Word, letters);
                return false; // Бонусные слова не засчитываются как правильные
                
            case WordType.Wrong:
                ProcessWrongWord(result.Word);
                return false;
        }
        
        return false;
    }
    
    /// <summary>
    /// Обрабатывает правильное слово (логика из LevelManager)
    /// </summary>
    private bool ProcessCorrectWord(string word, Letter[] letters)
    {
        Debug.Log($"✅ Correct word: {word}");
        
        // Удаляем слово из списка текущих слов
        _currentLevelWords.Remove(word);
        
        // Воспроизводим звук успеха (логика из LevelManager)
        if (SoundPlayer.Instance != null)
        {
            SoundPlayer.Instance.PlaySound(SoundActionType.AddWord);
        }
        
        // Вызываем событие
        OnCorrectWordFound?.Invoke(word);
        
        return true;
    }
    
    /// <summary>
    /// Обрабатывает бонусное слово - начисляет валюту и показывает сообщение
    /// </summary>
    private void ProcessBonusWord(string word, Letter[] letters)
    {
        Debug.Log($"🎁 Bonus word found: {word}");

        // Обрабатываем через централизованный сервис
        bool wasNewlyFound = false;
        if (BonusWordsService.Instance != null)
        {
            wasNewlyFound = BonusWordsService.Instance.ProcessFoundBonusWord(word);
        }

        // Начисляем валюту только если это новое найденное слово
        if (wasNewlyFound)
        {
            CurrencyManager.Add(100);
        }

        // Напрямую устанавливаем текст для бонусного слова (без использования локализации)
        if (wrongTextInfo != null)
        {
            string message = wasNewlyFound ? $"НАЙДЕНО БОНУСНОЕ СЛОВО: {word}" : $"УЖЕ НАЙДЕНО: {word}";
            wrongTextInfo.SetText(message);
        }

        // Показываем сообщение о бонусном слове
        if (_wrongTextDisplayCoroutine != null)
        {
            StopCoroutine(_wrongTextDisplayCoroutine);
        }

        _wrongTextDisplayCoroutine = StartCoroutine(ShowBonusTextInfo());

        // Воспроизводим звук успеха для бонусного слова
        if (SoundPlayer.Instance != null)
        {
            SoundPlayer.Instance.PlaySound(SoundActionType.AddWord);
        }

        // Вызываем событие для уведомления других систем
        OnBonusWordFound?.Invoke(word);
    }
    
    /// <summary>
    /// Обрабатывает неправильное слово (логика из LevelManager)
    /// </summary>
    private void ProcessWrongWord(string word)
    {
        Debug.Log($"❌ Wrong word: {word}");        
  
        if (SoundPlayer.Instance != null)
        {
            SoundPlayer.Instance.PlaySound(SoundActionType.CancelSelection);
        }
        
        // Вызываем событие
        OnWrongWordEntered?.Invoke(word);
    }
    
    /// <summary>
    /// Показывает сообщение о неправильном слове (логика из LevelManager)
    /// </summary>
    private IEnumerator ShowWrongTextInfo()
    {
        yield return new WaitForSeconds(wrongTextInfoDuration);

        if (wrongTextInfo != null)
        {
            wrongTextInfo.SetText(string.Empty);
        }
    }

    /// <summary>
    /// Показывает сообщение о бонусном слове
    /// </summary>
    private IEnumerator ShowBonusTextInfo()
    {
        yield return new WaitForSeconds(bonusTextInfoDuration);

        if (wrongTextInfo != null)
        {
            wrongTextInfo.SetText(string.Empty);
        }
    }

    /// <summary>
    /// Проверяет, остались ли основные слова для завершения уровня
    /// </summary>
    public bool HasRemainingWords()
    {
        return _currentLevelWords.Count > 0;
    }

    /// <summary>
    /// Получает количество оставшихся основных слов
    /// </summary>
    public int GetRemainingWordsCount()
    {
        return _currentLevelWords.Count;
    }

    /// <summary>
    /// Получает список оставшихся основных слов
    /// </summary>
    public List<string> GetRemainingWords()
    {
        return _currentLevelWords.ToList();
    }

    /// <summary>
    /// Получает количество доступных бонусных слов
    /// </summary>
    public int GetBonusWordsCount()
    {
        return BonusWordsService.Instance?.GetTotalWordsCount() ?? 0;
    }

    /// <summary>
    /// Очищает все слова (для перехода на новый уровень)
    /// </summary>
    public void ClearAllWords()
    {
        _currentLevelWords.Clear();
        // Бонусные слова теперь хранятся централизованно, очищать их не нужно

        if (_wrongTextDisplayCoroutine != null)
        {
            StopCoroutine(_wrongTextDisplayCoroutine);
            _wrongTextDisplayCoroutine = null;
        }

        if (wrongTextInfo != null)
        {
            wrongTextInfo.SetText(string.Empty);
        }
    }
}
