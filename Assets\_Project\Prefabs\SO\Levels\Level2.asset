%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a980bed33a1e6964d9e431f3aff7ab6a, type: 3}
  m_Name: Level2
  m_EditorClassIdentifier: 
  levelNum: 2
  lettersCount: 3
  rowsCount: 3
  columnsCount: 3
  backwardsProbability: 0
  fillMatrixes:
  - rows:
    - row:
      - Letter: "\u0441"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 0
      - Letter: "\u0433"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 0
      - Letter: "\u043E"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 1
    - row:
      - Letter: "\u0443"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 1
      - Letter: "\u0447"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 0
      - Letter: "\u0434"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 2
    - row:
      - Letter: "\u043F"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 2
      - Letter: "\u0430"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 1
      - Letter: "\u0439"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 2
  - rows:
    - row:
      - Letter: "\u0447"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 0
      - Letter: "\u0430"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 1
      - Letter: "\u0439"
        Word: "\u0447\u0430\u0439"
        IndexInWord: 2
    - row:
      - Letter: "\u0441"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 0
      - Letter: "\u0433"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 0
      - Letter: "\u043E"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 1
    - row:
      - Letter: "\u0443"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 1
      - Letter: "\u043F"
        Word: "\u0441\u0443\u043F"
        IndexInWord: 2
      - Letter: "\u0434"
        Word: "\u0433\u043E\u0434"
        IndexInWord: 2
  words:
  - "\u0433\u043E\u0434"
  - "\u0441\u0443\u043F"
  - "\u0447\u0430\u0439"
  bonusWords: []
